using System.Text.Json.Serialization;
using System;
using System.Collections.Generic;

namespace VisnamSignServerClient
{
    public class VisnamSignServerClientConfiguration
    {
        public string Domain { get; set; }
        public string Schema  { get; set; }
        public int Port { get; set; }
    }

    public class HouResponseDataModel<T>
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        public T Data { get; set; }
    }

    #region EndCert

    public class EndCertRequestModel
    {
        public string UserId { get; set; }
        public string UserKey { get; set; }
    }
    
    public class EndCertResponseModel
    {
        [JsonPropertyName("data")]
        public string Data { get; set; }
    }

    #endregion

    #region SignHash

    public class SignHashDataClientRequestModel
    {
        public string Base64Hash { get; set; }
        
        public string UserId { get; set; }
        
        public string UserKey { get; set; }
    }
    
    public class SignHashRequestModel
    {
        [JsonPropertyName("hashalg")]
        public string HashAlg { get; set; } = "SHA256";
        
        [JsonPropertyName("base64hash")]
        public string Base64Hash { get; set; }
    }
    
    public class SignHashResponseModel
    {
        [JsonPropertyName("status")]
        public int Status { get; set; }
        
        [JsonPropertyName("description")]
        public string Description { get; set; }
        
        [JsonPropertyName("error")]
        public string Error { get; set; }
        
        [JsonPropertyName("obj")]
        public string Obj { get; set; }
    }

    #endregion
}