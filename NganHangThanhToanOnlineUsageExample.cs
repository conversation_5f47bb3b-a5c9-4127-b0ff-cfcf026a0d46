using Core.Business;
using Core.Data;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.Examples
{
    /// <summary>
    /// V<PERSON> dụ sử dụng GetComboboxNganHangThanhToanOnlineQuery
    /// </summary>
    public class NganHangThanhToanOnlineUsageExample
    {
        private readonly IMediator _mediator;

        public NganHangThanhToanOnlineUsageExample(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// Ví dụ 1: Lấy tất cả ngân hàng thanh toán online
        /// </summary>
        public async Task<List<NganHangThanhToanOnlineSelectItemModel>> GetAllNganHangExample()
        {
            var query = new GetComboboxNganHangThanhToanOnlineQuery();
            var result = await _mediator.Send(query);
            
            Console.WriteLine($"Tổng số ngân hàng: {result.Count}");
            foreach (var item in result)
            {
                Console.WriteLine($"- {item.TenNganHang} ({item.MaNganHang}) - Active: {item.Active}, QR: {item.QrCode}");
            }
            
            return result;
        }

        /// <summary>
        /// Ví dụ 2: Lấy chỉ các ngân hàng đang hoạt động
        /// </summary>
        public async Task<List<NganHangThanhToanOnlineSelectItemModel>> GetActiveNganHangExample()
        {
            var query = new GetComboboxNganHangThanhToanOnlineQuery(
                count: 0, 
                textSearch: "", 
                active: true
            );
            var result = await _mediator.Send(query);
            
            Console.WriteLine($"Số ngân hàng đang hoạt động: {result.Count}");
            return result;
        }

        /// <summary>
        /// Ví dụ 3: Lấy các ngân hàng hỗ trợ QR Code
        /// </summary>
        public async Task<List<NganHangThanhToanOnlineSelectItemModel>> GetQrCodeSupportedBanksExample()
        {
            var query = new GetComboboxNganHangThanhToanOnlineQuery(
                count: 0, 
                textSearch: "", 
                active: true, 
                qrCode: true
            );
            var result = await _mediator.Send(query);
            
            Console.WriteLine($"Số ngân hàng hỗ trợ QR Code: {result.Count}");
            foreach (var item in result)
            {
                Console.WriteLine($"- {item.TenNganHang} - Cổng: {item.CongThanhToan}");
            }
            
            return result;
        }

        /// <summary>
        /// Ví dụ 4: Tìm kiếm ngân hàng theo từ khóa
        /// </summary>
        public async Task<List<NganHangThanhToanOnlineSelectItemModel>> SearchNganHangExample(string keyword)
        {
            var query = new GetComboboxNganHangThanhToanOnlineQuery(
                count: 0, 
                textSearch: keyword, 
                active: true
            );
            var result = await _mediator.Send(query);
            
            Console.WriteLine($"Tìm kiếm '{keyword}' - Kết quả: {result.Count}");
            foreach (var item in result)
            {
                Console.WriteLine($"- {item.TenNganHang} ({item.MaNganHang})");
            }
            
            return result;
        }

        /// <summary>
        /// Ví dụ 5: Lấy top 5 ngân hàng
        /// </summary>
        public async Task<List<NganHangThanhToanOnlineSelectItemModel>> GetTop5NganHangExample()
        {
            var query = new GetComboboxNganHangThanhToanOnlineQuery(
                count: 5, 
                textSearch: "", 
                active: true
            );
            var result = await _mediator.Send(query);
            
            Console.WriteLine($"Top 5 ngân hàng:");
            for (int i = 0; i < result.Count; i++)
            {
                var item = result[i];
                Console.WriteLine($"{i + 1}. {item.TenNganHang}");
            }
            
            return result;
        }

        /// <summary>
        /// Ví dụ 6: Lọc ngân hàng theo nhiều điều kiện
        /// </summary>
        public async Task<List<NganHangThanhToanOnlineSelectItemModel>> GetFilteredBanksExample()
        {
            // Lấy các ngân hàng đang hoạt động, hỗ trợ QR Code, có chứa từ "Vietcombank"
            var query = new GetComboboxNganHangThanhToanOnlineQuery(
                count: 10, 
                textSearch: "Vietcombank", 
                active: true, 
                qrCode: true
            );
            var result = await _mediator.Send(query);
            
            Console.WriteLine($"Ngân hàng Vietcombank hỗ trợ QR Code: {result.Count}");
            return result;
        }

        /// <summary>
        /// Ví dụ 7: Kiểm tra ngân hàng có hỗ trợ thanh toán online không
        /// </summary>
        public async Task<bool> CheckBankSupportOnlinePayment(string bankName)
        {
            var query = new GetComboboxNganHangThanhToanOnlineQuery(
                count: 0, 
                textSearch: bankName, 
                active: true
            );
            var result = await _mediator.Send(query);
            
            bool isSupported = result.Count > 0;
            Console.WriteLine($"Ngân hàng '{bankName}' {(isSupported ? "có" : "không")} hỗ trợ thanh toán online");
            
            return isSupported;
        }

        /// <summary>
        /// Ví dụ 8: Lấy danh sách ngân hàng cho dropdown/combobox
        /// </summary>
        public async Task<List<object>> GetBankDropdownDataExample()
        {
            var query = new GetComboboxNganHangThanhToanOnlineQuery(
                count: 0, 
                textSearch: "", 
                active: true
            );
            var result = await _mediator.Send(query);
            
            var dropdownData = new List<object>();
            foreach (var item in result)
            {
                dropdownData.Add(new
                {
                    Value = item.IdNganHang,
                    Text = $"{item.TenNganHang} {(item.QrCode ? "(QR)" : "")}",
                    Code = item.MaNganHang,
                    Gateway = item.CongThanhToan,
                    SupportsQR = item.QrCode
                });
            }
            
            Console.WriteLine($"Dữ liệu dropdown: {dropdownData.Count} items");
            return dropdownData;
        }

        /// <summary>
        /// Chạy tất cả ví dụ
        /// </summary>
        public async Task RunAllExamples()
        {
            Console.WriteLine("=== VÍ DỤ SỬ DỤNG GetComboboxNganHangThanhToanOnlineQuery ===\n");

            try
            {
                await GetAllNganHangExample();
                Console.WriteLine();

                await GetActiveNganHangExample();
                Console.WriteLine();

                await GetQrCodeSupportedBanksExample();
                Console.WriteLine();

                await SearchNganHangExample("Vietcombank");
                Console.WriteLine();

                await GetTop5NganHangExample();
                Console.WriteLine();

                await GetFilteredBanksExample();
                Console.WriteLine();

                await CheckBankSupportOnlinePayment("BIDV");
                Console.WriteLine();

                await GetBankDropdownDataExample();
                Console.WriteLine();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Program để test các ví dụ
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            // Cấu hình DI container (giả lập)
            var services = new ServiceCollection();
            // services.AddMediatR(...);
            // services.AddDbContext<SystemReadDataContext>(...);
            // services.AddScoped<ICacheService, ...>();
            
            var serviceProvider = services.BuildServiceProvider();
            var mediator = serviceProvider.GetService<IMediator>();
            
            if (mediator != null)
            {
                var example = new NganHangThanhToanOnlineUsageExample(mediator);
                await example.RunAllExamples();
            }
            else
            {
                Console.WriteLine("Cần cấu hình DI container để chạy ví dụ");
            }

            Console.WriteLine("\nNhấn phím bất kỳ để thoát...");
            Console.ReadKey();
        }
    }
}
