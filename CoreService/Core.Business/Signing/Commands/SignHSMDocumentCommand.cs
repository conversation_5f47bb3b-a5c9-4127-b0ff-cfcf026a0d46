using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Core.Shared;
using Core.Shared.Enums;
using iText.Bouncycastlefips.Cert;
using iText.Commons.Bouncycastle.Cert;
using iText.Forms.Form.Element;
using iText.IO.Image;
using iText.Kernel.Geom;
using iText.Kernel.Pdf;
using iText.Signatures;
using MediatR;
using Org.BouncyCastle.X509;
using Serilog;
using VisnamSignServerClient;


namespace Core.Business.Signing;

public class SignHSMDocumentCommand : IRequest<SignDocumentResponseModel>
{
    public SignDocumentRequestModel SignDocumentRequestModel { get; set; }

    public SignHSMDocumentCommand(SignDocumentRequestModel signDocumentRequestModel)
    {
        SignDocumentRequestModel = signDocumentRequestModel;
    }

    public class Handler : IRequestHandler<SignHSMDocumentCommand, SignDocumentResponseModel>
    {
        private readonly IMediator _mediator;
        private readonly VisnamSignServerApiClient _client;

        public Handler(IMediator mediator, VisnamSignServerApiClient client)
        {
            _client = client;
            _mediator = mediator;
        }

        public async Task<SignDocumentResponseModel> Handle(SignHSMDocumentCommand request,
            CancellationToken cancellationToken)
        {
            var model = request.SignDocumentRequestModel;
            Log.Information($"Ký HSM: {model.ChungThuSoId} - {model.MauChuKyId}");

            if (string.IsNullOrEmpty(model.FileBase64))
            {
                throw new ArgumentException("File ký không được để trống");
            }

            // Lấy thông tin CTS  
            var cts = await _mediator.Send(new GetChungThuSoByIdQuery(model.ChungThuSoId));
            if (cts == null)
            {
                throw new ArgumentException("Chứng thư số không tồn tại");
            }

            // Nếu không truyền cert sang thì thấy thông tin cert từ RA Service  
            X509CertificateParser parser = new X509CertificateParser();
            X509Certificate[] chain;

            // TODO: Cần bổ sung đủ chain cert  
            chain = new X509Certificate[1];
            chain[0] = parser.ReadCertificate(Convert.FromBase64String(cts.CertificateBase64));

            // Kiểm tra cert  
            foreach (var item in chain)
            {
                // Kiểm tra thời gian hiệu lực  
                if (!item.IsValidNow)
                {
                    Log.Information($"Chứng thư số hết hiệu lực");
                    throw new ArgumentException("Chứng thư số đã hết hiệu lực");
                }
            }
            
            MauChuKyModel mauChuKy = null;
            if (model.SignatureAppearanceConfiguration.IsVisible && model.MauChuKyId == null)
            {
                throw new ArgumentException("Mẫu chữ ký không được để trống");
            }
            else
            {
                // Lấy thông tin mẫu chữ ký  
                mauChuKy = await _mediator.Send(new GetMauChuKyByIdQuery(model.MauChuKyId.Value));
                if (mauChuKy == null)
                {
                    throw new ArgumentException("Mẫu chữ ký không tồn tại");
                }
            }

            // await _mediator.Send(new CheckRevokeCertByCRLCommand(chain, model.TraceId));  

            PdfReader reader = new PdfReader(Base64Convert.ConvertBase64ToMemoryStream(model.FileBase64));

            MemoryStream outputStream = new MemoryStream();

            try
            {
                PdfSigner signer = new PdfSigner(reader, outputStream, new StampingProperties().UseAppendMode());

                // iText 9 API - sử dụng SignerProperties
                var signerProperties = signer.GetSignerProperties();
                signerProperties
                    .SetReason("Lý do ký số")
                    .SetLocation("Vị trí ký")
                    .SetContact("Thông tin liên hệ");
                
                // Set field name và position
                var fieldName = Guid.NewGuid().ToString();
                signerProperties.SetFieldName(fieldName);
                
                // Nếu có  hình ảnh chữ ký
                if (mauChuKy != null && !string.IsNullOrEmpty(mauChuKy.ImageBase64))
                {
                    ImageData img = ImageDataFactory.Create(Convert.FromBase64String(mauChuKy.ImageBase64));
                    Rectangle rect;
                    if (mauChuKy.LoaiKySuDung == (short)LoaiKyEnum.KyNhay)
                    {
                        rect = new Rectangle(500, 190, 100, 50);
                    }
                    else
                    {
                        rect= new Rectangle(395, 100, 200, 100);
                    }
                
                    // iText 9 API - tạo SignatureFieldAppearance và set vào SignerProperties
                    var appearance = new SignatureFieldAppearance(fieldName)
                        .SetContent("", img); // Set image content
                
                    signerProperties
                        .SetSignatureAppearance(appearance)
                        .SetPageNumber(1)
                        .SetPageRect(rect);
                }

                // Tạo external signature cho HSM
                IExternalSignature pks = new VisnamHSMSignServerSignature(_client, _mediator, model.ChungThuSoId);

                // Convert chain sang IX509Certificate[]
                IX509Certificate[] certChain = new IX509Certificate[chain.Length];
                for (int i = 0; i < chain.Length; i++)
                {
                    var certBytes = chain[i].GetEncoded();
                    var fipsCert = new Org.BouncyCastle.Cert.X509Certificate(certBytes);
                    certChain[i] = new X509CertificateBCFips(fipsCert);
                }

                // Thực hiện ký số
                signer.SignDetached(pks, certChain, null, null, null, 0, PdfSigner.CryptoStandard.CADES);
            }
            catch (Exception ex)
            {
                Log.Error($"Có lỗi xảy ra: {ex.Message}", ex);
                throw ex;
            }

            return new SignDocumentResponseModel()
            {
                FileBase64 = Base64Convert.ConvertMemoryStreamToBase64(outputStream)
            };
        }
    }
}