using iText.Kernel.Crypto;
using iText.Signatures;
using System;
using System.Security.Cryptography;
using Core.Shared;
using MediatR;
using VisnamSignServerClient;

namespace Core.Business.Signing
{
    public class VisnamHSMSignServerSignature : IExternalSignature
    {
        private readonly VisnamSignServerApiClient _client;
        private readonly IMediator _mediator;
        private readonly int _certificateId;

        public VisnamHSMSignServerSignature(VisnamSignServerApiClient client, IMediator mediator, int certificateId)
        {
            _client = client;
            _certificateId = certificateId;
            _mediator = mediator;
        }
        
        public string GetHashAlgorithm()
        {
            return DigestAlgorithms.SHA256;
        }

        public string GetDigestAlgorithmName()
        {
            return DigestAlgorithms.SHA256;
        }

        public string GetEncryptionAlgorithm()
        {
            return "RSA";
        }
        public string GetSignatureAlgorithmName()
        {
            return "RSA";
        }

        public byte[] Sign(byte[] message)
        {
            try
            {
                var base64Hash = Convert.ToBase64String(message);

                // Lấy thông tin CTS
                var cts = _mediator.Send(new GetChungThuSoByIdQuery(_certificateId)).Result;
                if (cts == null)
                {
                    throw new ArgumentException($"Không tìm thấy chứng thư số với ID: {_certificateId}");
                }

                // Lấy thông tin chứng thư số từ bảng VisNamTaiKhoanKetNoi
                var visnamAccount = _mediator.Send(new GetVisnamTaiKhoanKetNoiByIdQuery(cts.ReferenceId)).Result;
                if (visnamAccount == null)
                {
                    throw new ArgumentException($"Không tìm thấy tài khoản kết nối Visnam với ReferenceId: {cts.ReferenceId}");
                }

                // Kiểm tra dữ liệu cần thiết
                if (string.IsNullOrEmpty(visnamAccount.Key))
                {
                    throw new ArgumentException("Key của tài khoản Visnam không được để trống");
                }

                if (string.IsNullOrEmpty(visnamAccount.Secret))
                {
                    throw new ArgumentException("Secret của tài khoản Visnam không được để trống");
                }

                // Thử decrypt secret với error handling
                string decryptedSecret;
                try
                {
                    decryptedSecret = AesEncryption.Decrypt(visnamAccount.Secret, AesEncryption.KeyDefault);
                    if (string.IsNullOrEmpty(decryptedSecret))
                    {
                        throw new Exception("Secret sau khi decrypt bị rỗng");
                    }
                }
                catch (Exception ex)
                {
                    throw new CryptographicException($"Lỗi khi decrypt secret: {ex.Message}. " +
                        $"Secret length: {visnamAccount.Secret?.Length ?? 0}, " +
                        $"Secret preview: {visnamAccount.Secret?.Substring(0, Math.Min(20, visnamAccount.Secret?.Length ?? 0))}...", ex);
                }

                var signHashResponse = _client.SignHashAsync(new SignHashDataClientRequestModel()
                {
                    Base64Hash = base64Hash,
                    UserId = visnamAccount.Key,
                    UserKey = decryptedSecret
                }).Result;

                if (signHashResponse == null)
                {
                    throw new Exception("API trả về response null");
                }

                if (signHashResponse.Status != 0)
                {
                    throw new Exception($"API trả về lỗi: {signHashResponse.Error} (Status: {signHashResponse.Status})");
                }

                if (string.IsNullOrEmpty(signHashResponse.Obj))
                {
                    throw new Exception("API trả về signature rỗng");
                }

                var signature = Convert.FromBase64String(signHashResponse.Obj);
                return signature;
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi trong quá trình ký số: {ex.Message}", ex);
            }
        }

        public ISignatureMechanismParams GetSignatureMechanismParameters()
        {
            // Không implement trong version này, trả về null như ExternalApiSignature
            // iText sẽ sử dụng default parameters
            return null;
        }
    }
}
