using Core.Data;
using Core.Data.Signing;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Security.Cryptography.X509Certificates;
using System.Threading;
using System.Threading.Tasks;
using Core.Shared.Enums;
using VisnamSignServerClient;

namespace Core.Business
{
    public class SyncDataCertFromVisnamCommand : IRequest<SyncDataCertFromVisnamResult>
    {
        public int Id { get; set; }

        /// <summary>
        /// Đồng bộ chứng thư số từ VisNam vào hệ thống
        /// </summary>
        /// <param name="id">ID dữ liệu cần đồng bộ chứng thư số</param>
        public SyncDataCertFromVisnamCommand(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<SyncDataCertFromVisnamCommand, SyncDataCertFromVisnamResult>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;
            private readonly VisnamSignServerApiClient _visnamClient;

            public Handler(
                SystemDataContext dataContext, 
                ICacheService cacheService, 
                IStringLocalizer<Resources> localizer, 
                Func<IContextAccessor> contextAccessorFactory,
                VisnamSignServerApiClient visnamClient)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
                _visnamClient = visnamClient;
            }

            public async Task<SyncDataCertFromVisnamResult> Handle(SyncDataCertFromVisnamCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                var result = new SyncDataCertFromVisnamResult();

                Log.Information($"Start sync certificates from VisNam for data ID: {id}");

                // Sử dụng transaction để đảm bảo tính toàn vẹn dữ liệu
                using var transaction = await _dataContext.Database.BeginTransactionAsync();
                try
                {
                    // 1. Lấy thông tin Key/Secret từ SgVisnamTaiKhoanKetNoi
                    var visnamAccount = await _dataContext.SgVisnamTaiKhoanKetNois
                        .FirstOrDefaultAsync(x => x.Id == id && x.IsActive, cancellationToken: cancellationToken);

                    if (visnamAccount == null)
                    {
                        throw new ArgumentException($"{_localizer["chung-thu-so.sync.visnam-account-not-found"]}");
                    }

                    // Decrypt secret
                    var decryptedSecret = AesEncryption.Decrypt(visnamAccount.Secret, AesEncryption.KeyDefault);

                    // 2. Gọi VisNam API để lấy danh sách chứng thư số
                    var endCertRequest = new EndCertRequestModel
                    {
                        UserId = visnamAccount.Key,
                        UserKey = decryptedSecret
                    };

                    Log.Information($"Calling VisNam API with Key: {visnamAccount.Key}");
                    var endCertResponse = await _visnamClient.GetEndCertAsync(endCertRequest);

                    if (endCertResponse == null || string.IsNullOrEmpty(endCertResponse.Data))
                    {
                        throw new Exception($"{_localizer["chung-thu-so.sync.no-data-from-visnam"]}");
                    }

                    // 3. Parse certificate từ base64 string
                    var certificateData = ParseCertificateFromBase64(endCertResponse.Data);
                    if (certificateData == null)
                    {
                        throw new Exception($"{_localizer["chung-thu-so.sync.parse-certificate-failed"]}");
                    }

                    Log.Information($"Parsed certificate - Serial: {certificateData.SerialNumber}, Subject: {certificateData.SubjectName}");

                    // 4. Kiểm tra và xử lý đồng bộ dữ liệu
                    var existingCert = await _dataContext.SgChungThuSos
                        .FirstOrDefaultAsync(x => x.SerialNumber == certificateData.SerialNumber, cancellationToken: cancellationToken);

                    if (existingCert == null)
                    {
                        // Tạo mới record
                        var newCert = new SgChungThuSo
                        {
                            SerialNumber = certificateData.SerialNumber,
                            CertificateBase64 = certificateData.CertificateBase64,
                            SubjectName = certificateData.SubjectName,
                            Issuer = certificateData.Issuer,
                            NotBefore = certificateData.NotBefore,
                            NotAfter = certificateData.NotAfter,
                            Source = (short) DoiTacKySoEnum.VisNamSignServer.GetHashCode(),
                            ReferenceId = visnamAccount.Id,
                            IsActive = true,
                            Order = 0,
                            CreatedDate = DateTime.Now,
                            CreatedUserId = _contextAccessor.UserId
                        };

                        await _dataContext.SgChungThuSos.AddAsync(newCert);
                        result.AddedCount++;

                        Log.Information($"Added new certificate: {certificateData.SerialNumber}");
                    }
                    else
                    {
                        // Cập nhật thông tin hiện có
                        existingCert.ReferenceId = visnamAccount.Id;
                        existingCert.CertificateBase64 = certificateData.CertificateBase64;
                        existingCert.SubjectName = certificateData.SubjectName;
                        existingCert.Issuer = certificateData.Issuer;
                        existingCert.NotBefore = certificateData.NotBefore;
                        existingCert.NotAfter = certificateData.NotAfter;
                        existingCert.ModifiedDate = DateTime.Now;
                        existingCert.ModifiedUserId = _contextAccessor.UserId;

                        _dataContext.SgChungThuSos.Update(existingCert);
                        result.UpdatedCount++;
                        _cacheService.Remove(ChungThuSoConstant.BuildCacheKey(existingCert.Id.ToString()));

                        Log.Information($"Updated existing certificate: {certificateData.SerialNumber}");
                    }

                    await _dataContext.SaveChangesAsync(cancellationToken);
                    await transaction.CommitAsync(cancellationToken);

                    result.IsSuccess = true;
                    result.Message = $"Đồng bộ thành công: {result.AddedCount} chứng thư số mới, {result.UpdatedCount} chứng thư số cập nhật";

                    // Clear cache liên quan đến ChungThuSo
                    _cacheService.Remove(ChungThuSoConstant.BuildCacheKey());

                    // Ghi log audit trail
                    _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                    {
                        Description = $"Đồng bộ chứng thư số từ VisNam cho User ID: {visnamAccount.UserId}. Kết quả: {result.Message}",
                        ObjectCode = VisnamTaiKhoanKetNoiConstant.CachePrefix,
                        ObjectId = id.ToString()
                    });

                    Log.Information($"Sync certificates completed successfully: {result.Message}");

                    return result;
                }
                catch (Exception ex)
                {
                    // Rollback transaction khi có lỗi
                    await transaction.RollbackAsync();
                    
                    result.IsSuccess = false;
                    result.Message = $"Lỗi đồng bộ chứng thư số: {ex.Message}";

                    // Log lỗi với đầy đủ context
                    Log.Error(ex, $"Error syncing certificates from VisNam for data ID: {id}. Error: {ex.Message}");
                    
                    throw new Exception($"Đồng bộ chứng thư số thất bại: {ex.Message}", ex);
                }
            }

            private CertificateData ParseCertificateFromBase64(string base64Data)
            {
                try
                {
                    // Remove any whitespace and newlines
                    var cleanBase64 = base64Data.Replace("\n", "").Replace("\r", "").Replace(" ", "");
                    
                    // Convert base64 to byte array
                    var certBytes = Convert.FromBase64String(cleanBase64);
                    
                    // Create X509Certificate2 object
                    var certificate = new X509Certificate2(certBytes);
                    
                    // Validate certificate
                    if (certificate == null)
                    {
                        throw new Exception("Certificate is null after parsing");
                    }

                    // Extract certificate information
                    return new CertificateData
                    {
                        SerialNumber = certificate.SerialNumber,
                        CertificateBase64 = cleanBase64,
                        SubjectName = certificate.Subject,
                        Issuer = certificate.Issuer,
                        NotBefore = certificate.NotBefore,
                        NotAfter = certificate.NotAfter
                    };
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"Error parsing certificate from base64: {ex.Message}");
                    throw new Exception($"Không thể parse certificate: {ex.Message}", ex);
                }
            }
        }
    }

    /// <summary>
    /// Kết quả của việc đồng bộ chứng thư số từ VisNam
    /// </summary>
    public class SyncDataCertFromVisnamResult
    {
        /// <summary>
        /// Trạng thái thành công
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Thông báo kết quả
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Số lượng chứng thư số được thêm mới
        /// </summary>
        public int AddedCount { get; set; }

        /// <summary>
        /// Số lượng chứng thư số được cập nhật
        /// </summary>
        public int UpdatedCount { get; set; }

        /// <summary>
        /// Tổng số chứng thư số được xử lý
        /// </summary>
        public int TotalProcessed => AddedCount + UpdatedCount;
    }

    /// <summary>
    /// Dữ liệu certificate được parse từ X509Certificate2
    /// </summary>
    public class CertificateData
    {
        public string SerialNumber { get; set; }
        public string CertificateBase64 { get; set; }
        public string SubjectName { get; set; }
        public string Issuer { get; set; }
        public DateTime NotBefore { get; set; }
        public DateTime NotAfter { get; set; }
    }
}
