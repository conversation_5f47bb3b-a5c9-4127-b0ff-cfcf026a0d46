using Core.Data;
using Core.Shared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Core.Business
{
    public class NganHangThanhToanOnlineSelectItemModel
    {
        public int IdNganHang { get; set; }
        public string MaNganHang { get; set; }
        public string TenNganHang { get; set; }
        public string CongThanhToan { get; set; }
        public bool Active { get; set; }
        public bool QrCode { get; set; }
    }

    public class NganHangThanhToanOnlineBaseModel
    {
        public int IdNganHang { get; set; }
        public string MaNganHang { get; set; }
        public string TenNganHang { get; set; }
        public string CongThanhToan { get; set; }
        public bool Active { get; set; }
        public bool QrCode { get; set; }
    }

    public class NganHangThanhToanOnlineModel : NganHangThanhToanOnlineBaseModel
    {

    }

    public class NganHangThanhToanOnlineFilterModel : BaseQueryFilterModel
    {
        public NganHangThanhToanOnlineFilterModel()
        {
            Ascending = "desc";
            PropertyName = "IdNganHang";
        }

        public bool? Active { get; set; }
        public bool? QrCode { get; set; }
    }

    public class CreateNganHangThanhToanOnlineModel
    {
        [MaxLength(50, ErrorMessage = "NganHangThanhToanOnline.MaNganHang.MaxLength(50)")]
        public string MaNganHang { get; set; }

        [MaxLength(200, ErrorMessage = "NganHangThanhToanOnline.TenNganHang.MaxLength(200)")]
        [Required(ErrorMessage = "NganHangThanhToanOnline.TenNganHang.NotRequire")]
        public string TenNganHang { get; set; }

        [MaxLength(200, ErrorMessage = "NganHangThanhToanOnline.CongThanhToan.MaxLength(200)")]
        public string CongThanhToan { get; set; }

        [Required(ErrorMessage = "NganHangThanhToanOnline.Active.NotRequire")]
        public bool Active { get; set; } = true;

        public bool QrCode { get; set; } = false;
    }

    public class UpdateNganHangThanhToanOnlineModel
    {
        [Required(ErrorMessage = "NganHangThanhToanOnline.IdNganHang.NotRequire")]
        public int IdNganHang { get; set; }

        [MaxLength(50, ErrorMessage = "NganHangThanhToanOnline.MaNganHang.MaxLength(50)")]
        public string MaNganHang { get; set; }

        [MaxLength(200, ErrorMessage = "NganHangThanhToanOnline.TenNganHang.MaxLength(200)")]
        [Required(ErrorMessage = "NganHangThanhToanOnline.TenNganHang.NotRequire")]
        public string TenNganHang { get; set; }

        [MaxLength(200, ErrorMessage = "NganHangThanhToanOnline.CongThanhToan.MaxLength(200)")]
        public string CongThanhToan { get; set; }

        [Required(ErrorMessage = "NganHangThanhToanOnline.Active.NotRequire")]
        public bool Active { get; set; }

        public bool QrCode { get; set; }

        public void UpdateEntity(SvNganHangThanhToanOnline entity)
        {
            entity.IdNganHang = IdNganHang;
            entity.MaNganHang = MaNganHang;
            entity.TenNganHang = TenNganHang;
            entity.CongThanhToan = CongThanhToan;
            entity.Active = Active;
            entity.QrCode = QrCode;
        }
    }

    public class CreateManyNganHangThanhToanOnlineModel
    {
        public List<CreateNganHangThanhToanOnlineModel> ListNganHangThanhToanOnlineModels { get; set; }
    }

    public class UpdateManyNganHangThanhToanOnlineModel
    {
        public List<UpdateNganHangThanhToanOnlineModel> ListNganHangThanhToanOnlineModels { get; set; }
    }

    public class NganHangThanhToanOnlineQueryFilter : BaseQueryFilterModel
    {
        public bool? Active { get; set; }
        public bool? QrCode { get; set; }
    }
}
