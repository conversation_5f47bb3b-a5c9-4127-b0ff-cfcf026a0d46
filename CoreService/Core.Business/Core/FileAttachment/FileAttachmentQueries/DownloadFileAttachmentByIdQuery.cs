using System.Linq;
using Core.Data;
using Core.Shared;
using MediatR;
using System.Threading.Tasks;
using System.Threading;
using Microsoft.EntityFrameworkCore;
using System;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Shared.Enums;
using System.IO;
using Serilog;

namespace Core.Business
{
    public class DownloadFileAttachmentByIdQuery : IRequest<Base64FileDataResponse>
    {
        public Guid Id { get; set; }

        /// <summary>
        /// Lấy thông tin file đính kèm theo id
        /// </summary>
        /// <param name="id">Id trình độ đào tạo</param>
        public DownloadFileAttachmentByIdQuery(Guid id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<DownloadFileAttachmentByIdQuery, Base64FileDataResponse>
        {
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IMediator _mediator;
            private readonly IConfiguration _config;
            private readonly IWebHostEnvironment _webHostEnvironment;

            public Handler(
                IStringLocalizer<Resources> localizer, IMediator mediator,
                IConfiguration config, IWebHostEnvironment webHostEnvironment
                )
            {
                _localizer = localizer;
                _mediator = mediator;
                _config = config;
                _webHostEnvironment = webHostEnvironment;
            }

            public async Task<Base64FileDataResponse> Handle(DownloadFileAttachmentByIdQuery request, CancellationToken cancellationToken)
            {
                var fileDinhKem = await _mediator.Send(new GetFileAttachmentByIdQuery(request.Id));

                UploadConfig uploadConfig = _config.GetSection("AppSettings:UploadConfig").Get<UploadConfig>();

                if (uploadConfig == null)
                    uploadConfig = new UploadConfig();

                
                if (fileDinhKem == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }

                if (fileDinhKem.Source == SourceFileEnums.Server.GetHashCode())
                {
                    var fullFilePath = Path.Combine(uploadConfig.FolderUpload, fileDinhKem.FilePath);

                    if (File.Exists(fullFilePath))
                    {
                        // Đọc nội dung của tệp vào một mảng byte
                        byte[] fileBytes = await File.ReadAllBytesAsync(fullFilePath);

                        // Chuyển mảng byte thành dạng Base64
                        return new Base64FileDataResponse
                        {
                            FileData = Convert.ToBase64String(fileBytes),
                            FileName = fileDinhKem.FileName
                        };
                    }
                    else
                    {
                        Log.Error($"File not found: {fullFilePath}");
                        throw new ArgumentException("File not found");
                    }
                }
                else if (fileDinhKem.Source == SourceFileEnums.MinIO.GetHashCode())
                {
                    var ms = new MinIOService(_config);
                    var memory = await ms.DownloadObjectAsync(fileDinhKem.BucketName, fileDinhKem.ObjectName);
                    memory.Position = 0;

                    return new Base64FileDataResponse
                    {
                        FileData = Convert.ToBase64String(memory.ToArray()),
                        FileName = fileDinhKem.FileName
                    };
                }
                else
                {
                    throw new ArgumentException("File not found");
                }
            }
        }
    }
}
