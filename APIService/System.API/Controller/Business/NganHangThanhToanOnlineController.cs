using Core.API.Shared;
using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace System.API.Controller.Business
{
    [Route("api/ngan-hang-thanh-toan-online")]
    [ApiController]
    [Authorize]
    public class NganHangThanhToanOnlineController : ApiControllerBase
    {
        public NganHangThanhToanOnlineController(IMediator mediator, IStringLocalizer<Resources> localizer, IConfiguration config) : base(mediator, localizer, config)
        {
        }

        /// <summary>
        /// Lấy danh sách ngân hàng thanh toán online cho combobox
        /// </summary>
        /// <param name="count">Số bản ghi tối đa</param>
        /// <param name="ts"><PERSON>ừ khóa tìm kiếm</param>
        /// <param name="active"><PERSON><PERSON><PERSON> theo trạng thái hoạt động (true/false)</param>
        /// <param name="qrCode">Lọc theo hỗ trợ QR Code (true/false)</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<NganHangThanhToanOnlineSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "", bool? active = null, bool? qrCode = null)
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxNganHangThanhToanOnlineQuery(count, ts, active, qrCode));
            });
        }

        /// <summary>
        /// Lấy danh sách ngân hàng thanh toán online đang hoạt động cho combobox
        /// </summary>
        /// <param name="count">Số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox/active")]
        [ProducesResponseType(typeof(ResponseObject<List<NganHangThanhToanOnlineSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetActiveListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxNganHangThanhToanOnlineQuery(count, ts, active: true));
            });
        }

        /// <summary>
        /// Lấy danh sách ngân hàng hỗ trợ QR Code cho combobox
        /// </summary>
        /// <param name="count">Số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox/qr-supported")]
        [ProducesResponseType(typeof(ResponseObject<List<NganHangThanhToanOnlineSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetQrSupportedListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxNganHangThanhToanOnlineQuery(count, ts, active: true, qrCode: true));
            });
        }

        /// <summary>
        /// Lấy danh sách ngân hàng thanh toán online đang hoạt động và hỗ trợ QR Code
        /// </summary>
        /// <param name="count">Số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox/active-qr")]
        [ProducesResponseType(typeof(ResponseObject<List<NganHangThanhToanOnlineSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetActiveQrSupportedListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async (RequestUser u) =>
            {
                return await _mediator.Send(new GetComboboxNganHangThanhToanOnlineQuery(count, ts, active: true, qrCode: true));
            });
        }
    }
}
