using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Shared;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using Core.API.Shared;
using Core.Business.Service.CheckConnectionService;
using UAParser;
using System.Linq;
using Core.Shared.ContextAccessor;
using System;
using Core.Business.System.Pdf;
using Core.Shared.Model;
using System.IO;
using System.Collections.Generic;
using System.Security.Claims;
using Core.Data;
using Microsoft.AspNetCore.Hosting;
using Core.Business.Signing;
using Core.Business;

namespace Core.API.Controller
{
    /// <summary>
    /// Module test service
    /// </summary>
    [ApiController]
    [Route("system/v1/test")]
    [ApiExplorerSettings(GroupName = "00. Test", IgnoreApi = false)]
    [AllowAnonymous]
    public class TestController : ApiControllerBaseV2
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        public TestController(
            IWebHostEnvironment webHostEnvironment,
            Func<IContextAccessor> contextAccessorFactory,
            IMediator mediator,
            IStringLocalizer<Resources> localizer,
            IConfiguration config) : base(contextAccessorFactory, mediator, localizer, config)
        {
            _webHostEnvironment = webHostEnvironment;
        }

        /// <summary>
        /// Test Connection
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("connection")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> ConnectionCheckQuery()
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new ConnectionCheckQuery());
            });
        }

        /// <summary>
        /// Get Client info
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("get-client-info")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetClientInfo()
        {
            return await ExecuteFunction(async () =>
            {
                string clientIp = HttpContext.Connection.RemoteIpAddress?.ToString();
                var ip = HttpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
                var realIp = HttpContext.Request.Headers["X-Real-Ip"].FirstOrDefault();
                string userAgent = HttpContext.Request.Headers["User-Agent"].ToString();
                string requestMethod = HttpContext.Request.Method;
                string requestPath = HttpContext.Request.Path;

                var parser = Parser.GetDefault();
                var clientInfo = parser.Parse(userAgent);

                // get all header
                var headers = HttpContext.Request.Headers;

                var rs = new
                {
                    ClientIP = Helper.GetIPAddress(HttpContext.Request),
                    RealIp = realIp ?? "",
                    ForwardedForIP = ip ?? "",
                    RemoteIpAddress = clientIp,
                    UserAgent = userAgent,
                    RequestMethod = requestMethod,
                    RequestPath = requestPath,
                    Os = clientInfo.OS.ToString(),
                    Browser = clientInfo.UA.ToString(),
                    ClientInfo = clientInfo.ToString(),
                    headers = headers,
                };

                return rs;
            });
        }

        #region Api key

        /// <summary>
        /// Endpoint demo lấy thông tin user từ API Key
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("api-key/user-info")]
        [AllowAnonymous]
        [ApiKeyAuthentication]
        [ProducesResponseType(typeof(ResponseObject<object>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUserInfo()
        {
            return await ExecuteFunction(async () =>
            {
                var apiKey = HttpContext.User.FindFirst(ClaimConstants.API_KEY)?.Value;

                // Lấy thông tin từ HttpContext Items
                var apiKeyEntity = HttpContext.Items["ApiKey"] as ApiKey;

                return new
                {
                    Message = "Thông tin người dùng từ API Key Authentication",
                    User = new
                    {
                        Id = _contextAccessor.UserId,
                        UserName = _contextAccessor.UserName,
                        IsAuthenticated = HttpContext.User.Identity.IsAuthenticated,
                        AuthenticationType = HttpContext.User.Identity.AuthenticationType
                    },
                    ApiKeyInfo = new
                    {
                        Id = apiKeyEntity?.Id,
                        Key = apiKey,
                        ValidFrom = apiKeyEntity?.ValidFrom,
                        ValidTo = apiKeyEntity?.ValidTo,
                        Description = apiKeyEntity?.Description,
                        IsActive = apiKeyEntity?.IsActive
                    },
                    AccessTime = DateTime.Now
                };
            });
        }

        #endregion
        
        #region Docx to pdf

        /// <summary>
        /// Docx to PDF from Base64
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("docx-to-pdf-from-base64")]
        [ProducesResponseType(typeof(ResponseObject<FileBase64Response>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> DocxToPDF([FromBody] PdfConvertFromWordBase64Model request)
        {
            return await ExecuteFunction(async () =>
            {
                // Đọc file từ thư mục và convert về base64
                return await _mediator.Send(new ConvertPDFFromWordAndMetaDataCommand(request));
            });
        }

        /// <summary>
        /// Docx to PDF
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("docx-to-pdf-from-file-upload")]
        [ProducesResponseType(typeof(ResponseObject<FileBase64Response>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DocxToPDFFromFileUpload(IFormFile file)
        {
            return await ExecuteFunction(async () =>
            {
                if (file == null || file.Length == 0)
                    throw new ArgumentException("File is null");

                // Lấy định dạng file
                var fileExtension = Path.GetExtension(file.FileName).ToLower();

                // Kiểm tra định dạng file
                var allowedExtensions = new List<string>() { ".docx" };
                if (!allowedExtensions.Contains(fileExtension))
                {
                    throw new ArgumentException("Invalid file format");
                }

                using (var memoryStream = new MemoryStream())
                {
                    await file.CopyToAsync(memoryStream);
                    byte[] fileBytes = memoryStream.ToArray();

                    PdfConvertFromWordBase64Model request = new PdfConvertFromWordBase64Model
                    {
                        FileName = file.FileName,
                        FileBase64 = Convert.ToBase64String(fileBytes),
                        Metadatas = new List<KeyValueModel>()
                    };

                    // Đọc file từ thư mục và convert về base64
                    return await _mediator.Send(new ConvertPDFFromWordAndMetaDataCommand(request));
                }
            });
        }

        /// <summary>
        /// Docx to PDF
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("docx-to-pdf-from-template")]
        [ProducesResponseType(typeof(ResponseObject<FileBase64Response>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DocxToPDFFromFileTemplate()
        {
            return await ExecuteFunction(async () =>
            {
                var filePath = "templates/template-word.docx"; // Đường dẫn đến tệp mẫu
                var fileName = Path.GetFileName(filePath).ToLower();
                var fullFilePath = Path.Combine(_webHostEnvironment.WebRootPath, filePath);

                if (!System.IO.File.Exists(fullFilePath))
                    throw new ArgumentException("File not found");

                byte[] fileBytes = await System.IO.File.ReadAllBytesAsync(fullFilePath);

                PdfConvertFromWordBase64Model request = new PdfConvertFromWordBase64Model
                {
                    FileName = fileName,
                    FileBase64 = Convert.ToBase64String(fileBytes),
                    Metadatas = new List<KeyValueModel>()
                };

                // Đọc file từ thư mục và convert về base64
                return await _mediator.Send(new ConvertPDFFromWordAndMetaDataCommand(request));

            });
        }

        /// <summary>
        /// Docx to PDF
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("docx-to-pdf-from-template-with-table")]
        [ProducesResponseType(typeof(ResponseObject<FileBase64Response>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DocxToPDFFromFileTemplateWithTableData()
        {
            return await ExecuteFunction(async () =>
            {
                var filePath = "templates/template-word.docx"; // Đường dẫn đến tệp mẫu
                var fileName = Path.GetFileName(filePath).ToLower();
                var fullFilePath = Path.Combine(_webHostEnvironment.WebRootPath, filePath);

                if (!System.IO.File.Exists(fullFilePath))
                    throw new ArgumentException("File not found");

                byte[] fileBytes = await System.IO.File.ReadAllBytesAsync(fullFilePath);

                PdfConvertFromWordBase64Model request = new PdfConvertFromWordBase64Model
                {
                    FileName = fileName,
                    FileBase64 = Convert.ToBase64String(fileBytes),
                    Metadatas = new List<KeyValueModel>() { 
                        new KeyValueModel() { Key = "{date}", Value = DateTime.Now.ToString() } ,
                        new KeyValueModel() { Key = "{documentName}", Value = "Document test DocxToPDFFromFileTemplateWithTableData" } 
                    },
                    TableDatas = new List<TableDataModel>
                    {
                        new TableDataModel
                        {
                            TableName = "{#Bang1#}",
                            TableData = new List<Dictionary<string, string>>()
                            {
                                new Dictionary<string, string>() { { "{index}", "1" }, { "{name}", "Nguyễn Văn A" }, { "{order}", "101" } },
                                new Dictionary<string, string>() { { "{index}", "2" }, { "{name}", "Trần Thị B" }, { "{order}", "102" } }
                            }
                        },
                        new TableDataModel
                        {
                            TableName = "{#Bang2#}",
                            TableData = new List<Dictionary<string, string>>()
                            {
                                new Dictionary<string, string>() { { "{index}", "1.1" }, { "{name}", "Nguyễn Văn A.1" }, { "{order}", "101.1" } },
                                new Dictionary<string, string>() { { "{index}", "2.1" }, { "{name}", "Trần Thị B.1" }, { "{order}", "102.1" } }
                            }
                        },
                        new TableDataModel
                        {
                            TableName = "{#Bang3#}",
                            TableData = new List<Dictionary<string, string>>()
                            {
                                new Dictionary<string, string>() { { "{index}", "1.12" }, { "{name}", "Nguyễn Văn A.12" }, { "{order}", "101.12" } },
                                new Dictionary<string, string>() { { "{index}", "2.12" }, { "{name}", "Trần Thị B.12" }, { "{order}", "102.12" } }
                            }
                        }
                    }
                };

                // Đọc file từ thư mục và convert về base64
                return await _mediator.Send(new ConvertPDFFromWordAndMetaDataCommand(request));
            });
        }

        #endregion

        #region Signing

        /// <summary>
        /// Test ký số HSM sử dụng file KY_TET.pdf
        /// </summary>
        /// <param name="chungThuSoId">ID chứng thư số</param>
        /// <param name="mauChuKyId">ID mẫu chữ ký</param>
        /// <returns></returns>
        [HttpPost, Route("test-sign-hsm")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> TestSignHSM([FromQuery] int chungThuSoId = 1, [FromQuery] int mauChuKyId = 1)
        {
            return await ExecuteFunction<object>(async () =>
            {
                try
                {
                    // Đường dẫn đến file KY_TET.pdf
                    var filePath = Path.Combine(_webHostEnvironment.WebRootPath, "KY_TET.pdf");

                    // Kiểm tra file có tồn tại không
                    if (!System.IO.File.Exists(filePath))
                    {
                        throw new FileNotFoundException($"Không tìm thấy file KY_TET.pdf tại đường dẫn: {filePath}");
                    }

                    // Đọc file và convert sang base64
                    var fileBytes = await System.IO.File.ReadAllBytesAsync(filePath);
                    var fileBase64 = Convert.ToBase64String(fileBytes);

                    // Tạo request model
                    var signRequest = new SignDocumentRequestModel
                    {
                        FileBase64 = fileBase64,
                        ChungThuSoId = chungThuSoId,
                        MauChuKyId = mauChuKyId
                    };

                    // Gọi command để ký
                    var command = new SignHSMDocumentCommand(signRequest);
                    var result = await _mediator.Send(command);

                    // Lưu file đã ký ra thư mục wwwroot
                    string savedFilePath = null;
                    string savedFileName = "Ky_test_signed.pdf";
                    try
                    {
                        if (!string.IsNullOrEmpty(result.FileBase64))
                        {
                            // Convert base64 thành byte array
                            var signedFileBytes = Convert.FromBase64String(result.FileBase64);

                            // Tạo đường dẫn file đầy đủ
                            savedFilePath = Path.Combine(_webHostEnvironment.WebRootPath, savedFileName);

                            // Lưu file
                            await System.IO.File.WriteAllBytesAsync(savedFilePath, signedFileBytes);
                        }
                    }
                    catch (Exception saveEx)
                    {
                        // Log lỗi nhưng không throw để không ảnh hưởng đến kết quả chính
                        // Có thể log lỗi ở đây nếu cần
                    }

                    // Trả về kết quả
                    return new
                    {
                        Success = true,
                        Message = "Ký số thành công",
                        Data = new
                        {
                            OriginalFileSize = fileBytes.Length,
                            ChungThuSoId = chungThuSoId,
                            MauChuKyId = mauChuKyId,
                            SavedFile = new
                            {
                                FileName = savedFileName,
                                FilePath = savedFilePath,
                                IsSaved = !string.IsNullOrEmpty(savedFilePath)
                            }
                        }
                    };
                }
                catch (Exception ex)
                {
                    return new
                    {
                        Success = false,
                        Message = $"Lỗi khi ký số: {ex.Message}",
                        Error = ex.ToString()
                    };
                }
            });
        }

        #endregion
    }
}
