using Core.API.Shared;
using Core.Business;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.API.Controller
{
    [ApiController]
    [Route("system/v1/chung-thu-so")]
    [ApiExplorerSettings(GroupName = "09. ChungThuSo (Chứng thư số)")]
    [Authorize]
    public class ChungThuSoController : ApiControllerBaseV2
    {
        public ChungThuSoController(
            Func<IContextAccessor> contextAccessorFactory,
            IMediator mediator,
            IStringLocalizer<Resources> localizer,
            IConfiguration config) : base(contextAccessorFactory, mediator, localizer, config)
        {
        }

        #region CRUD

        /// <summary>
        /// Thêm mới chứng thư số
        /// </summary>
        /// <param name="model">Thông tin chứng thư số</param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUNG_THU_SO_ADD))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] CreateChungThuSoModel model)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_CHUNG_THU_SO_CREATE);
                _contextAccessor.SystemLog.ActionName = LogConstants.ACTION_CHUNG_THU_SO_CREATE;

                if (_contextAccessor.UserId.HasValue)
                    model.UserId = _contextAccessor.UserId.Value;
                else
                    throw new ArgumentException("User ID is required");
                
                return await _mediator.Send(new CreateChungThuSoCommand(model));
            });
        }

        /// <summary>
        /// Thêm mới nhiều chứng thư số cùng lúc (bulk insert)
        /// </summary>
        /// <param name="model">Danh sách thông tin chứng thư số</param>
        /// <returns></returns>
        [HttpPost, Route("bulk")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUNG_THU_SO_ADD))]
        [ProducesResponseType(typeof(ResponseObject<CreateManyChungThuSoResult>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateMany([FromBody] CreateManyChungThuSoModel model)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_CHUNG_THU_SO_CREATE);
                _contextAccessor.SystemLog.ActionName = LogConstants.ACTION_CHUNG_THU_SO_CREATE;

                if (_contextAccessor.UserId.HasValue)
                    foreach (var item in model.ChungThuSos)
                    {
                        item.UserId = _contextAccessor.UserId.Value;
                    }
                else
                    throw new ArgumentException("User ID is required");
                
                return await _mediator.Send(new CreateManyChungThuSoCommand(model));
            });
        }

        /// <summary>
        /// Cập nhật chứng thư số
        /// </summary>
        /// <param name="model">Thông tin chứng thư số</param>
        /// <returns></returns>
        [HttpPut, Route("")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUNG_THU_SO_EDIT))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update([FromBody] UpdateChungThuSoModel model)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_CHUNG_THU_SO_UPDATE);
                _contextAccessor.SystemLog.ActionName = LogConstants.ACTION_CHUNG_THU_SO_UPDATE;

                return await _mediator.Send(new UpdateChungThuSoCommand(model));
            });
        }

        /// <summary>
        /// Xóa chứng thư số
        /// </summary>
        /// <param name="id">Id chứng thư số</param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUNG_THU_SO_DELETE))]
        [ProducesResponseType(typeof(ResponseObject<Unit>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(int id)
        {
            return await ExecuteFunction(async () =>
            {
                _contextAccessor.SystemLog.ActionCode = nameof(LogConstants.ACTION_CHUNG_THU_SO_DELETE);
                _contextAccessor.SystemLog.ActionName = LogConstants.ACTION_CHUNG_THU_SO_DELETE;

                return await _mediator.Send(new DeleteChungThuSoCommand(id));
            });
        }

        /// <summary>
        /// Lấy thông tin chứng thư số theo Id
        /// </summary>
        /// <param name="id">Id chứng thư số</param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUNG_THU_SO_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<ChungThuSoModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(int id)
        {
            return await ExecuteFunction(async () => { return await _mediator.Send(new GetChungThuSoByIdQuery(id)); });
        }

        #endregion

        #region List

        /// <summary>
        /// Lấy danh sách chứng thư số theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Điều kiện lọc</param>
        /// <returns></returns>
        [HttpPost, Route("filter")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUNG_THU_SO_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<PaginationList<ChungThuSoBaseModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Filter([FromBody] ChungThuSoQueryFilter filter)
        {
            return await ExecuteFunction(async () =>
            {
                filter.UserId = _contextAccessor.UserId;
                return await _mediator.Send(new GetFilterChungThuSoQuery(filter));
            });
        }

        /// <summary>
        /// Lấy danh sách chứng thư số cho theo người dùng đang đăng nhập
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-user")]
        [ProducesResponseType(typeof(ResponseObject<List<ChungThuSoSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListForUser(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async () =>
            {
                var result = await _mediator.Send(new GetComboboxChungThuSoQuery(count, ts, _contextAccessor.UserId));

                return result;
            });
        }

        /// <summary>
        /// Lấy danh sách chứng thư số cho combobox
        /// </summary>
        /// <param name="count">số bản ghi tối đa</param>
        /// <param name="ts">Từ khóa tìm kiếm</param>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("for-combobox")]
        [ProducesResponseType(typeof(ResponseObject<List<ChungThuSoSelectItemModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetListCombobox(int count = 0, string ts = "")
        {
            return await ExecuteFunction(async () =>
            {
                var result = await _mediator.Send(new GetComboboxChungThuSoQuery(count, ts));

                return result;
            });
        }

        /// <summary>
        /// Lấy thống kê chứng thư số
        /// </summary>
        /// <response code="200">Thành công</response>
        [HttpGet, Route("statistics")]
        [ClaimRequirement(ClaimConstants.PERMISSTTIONS, nameof(PermissionSystemEnum.CHUNG_THU_SO_VIEW))]
        [ProducesResponseType(typeof(ResponseObject<ChungThuSoStatisticsModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetStatistics()
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new GetChungThuSoStatisticsQuery());
            });
        }

        #endregion
    }
}