<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Combobox Ngân Hàng Thanh Toán Online</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .result-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
        }
        .bank-item {
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        .bank-item:last-child {
            border-bottom: none;
        }
        .badge-qr {
            background-color: #28a745;
        }
        .badge-active {
            background-color: #007bff;
        }
        .badge-inactive {
            background-color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">Demo Combobox Ngân Hàng Thanh Toán Online</h1>

        <!-- Demo 1: Combobox cơ bản -->
        <div class="demo-section">
            <h3>1. Combobox Cơ Bản - Tất Cả Ngân Hàng</h3>
            <div class="row">
                <div class="col-md-6">
                    <label for="bankSelect1" class="form-label">Chọn ngân hàng:</label>
                    <select id="bankSelect1" class="form-select">
                        <option value="">-- Chọn ngân hàng --</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label">Thông tin ngân hàng đã chọn:</label>
                    <div id="bankInfo1" class="result-box">
                        <em>Chưa chọn ngân hàng nào</em>
                    </div>
                </div>
            </div>
        </div>

        <!-- Demo 2: Combobox chỉ ngân hàng đang hoạt động -->
        <div class="demo-section">
            <h3>2. Combobox Ngân Hàng Đang Hoạt Động</h3>
            <div class="row">
                <div class="col-md-6">
                    <label for="bankSelect2" class="form-label">Chọn ngân hàng (chỉ đang hoạt động):</label>
                    <select id="bankSelect2" class="form-select">
                        <option value="">-- Chọn ngân hàng --</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label">Thông tin ngân hàng đã chọn:</label>
                    <div id="bankInfo2" class="result-box">
                        <em>Chưa chọn ngân hàng nào</em>
                    </div>
                </div>
            </div>
        </div>

        <!-- Demo 3: Combobox ngân hàng hỗ trợ QR Code -->
        <div class="demo-section">
            <h3>3. Combobox Ngân Hàng Hỗ Trợ QR Code</h3>
            <div class="row">
                <div class="col-md-6">
                    <label for="bankSelect3" class="form-label">Chọn ngân hàng (hỗ trợ QR Code):</label>
                    <select id="bankSelect3" class="form-select">
                        <option value="">-- Chọn ngân hàng --</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label">Thông tin ngân hàng đã chọn:</label>
                    <div id="bankInfo3" class="result-box">
                        <em>Chưa chọn ngân hàng nào</em>
                    </div>
                </div>
            </div>
        </div>

        <!-- Demo 4: Combobox với tìm kiếm -->
        <div class="demo-section">
            <h3>4. Combobox Với Tìm Kiếm (Select2)</h3>
            <div class="row">
                <div class="col-md-6">
                    <label for="bankSelect4" class="form-label">Tìm kiếm và chọn ngân hàng:</label>
                    <select id="bankSelect4" class="form-select" style="width: 100%;">
                        <option value="">-- Tìm kiếm ngân hàng --</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label">Thông tin ngân hàng đã chọn:</label>
                    <div id="bankInfo4" class="result-box">
                        <em>Chưa chọn ngân hàng nào</em>
                    </div>
                </div>
            </div>
        </div>

        <!-- Demo 5: Hiển thị danh sách tất cả ngân hàng -->
        <div class="demo-section">
            <h3>5. Danh Sách Tất Cả Ngân Hàng</h3>
            <div class="row">
                <div class="col-md-4">
                    <button id="loadAllBanks" class="btn btn-primary">Tải Tất Cả Ngân Hàng</button>
                    <button id="loadActiveBanks" class="btn btn-success">Chỉ Ngân Hàng Hoạt Động</button>
                    <button id="loadQrBanks" class="btn btn-info">Chỉ Hỗ Trợ QR</button>
                </div>
                <div class="col-md-8">
                    <div id="bankList" class="result-box">
                        <em>Nhấn nút để tải danh sách ngân hàng</em>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        // Cấu hình API base URL
        const API_BASE_URL = '/api/ngan-hang-thanh-toan-online';
        
        // Mock data cho demo (thay thế bằng API thực tế)
        const mockBankData = [
            {
                idNganHang: 1,
                maNganHang: "VCB",
                tenNganHang: "Ngân hàng TMCP Ngoại thương Việt Nam (Vietcombank)",
                congThanhToan: "VCB Gateway",
                active: true,
                qrCode: true
            },
            {
                idNganHang: 2,
                maNganHang: "BIDV",
                tenNganHang: "Ngân hàng TMCP Đầu tư và Phát triển Việt Nam (BIDV)",
                congThanhToan: "BIDV Gateway",
                active: true,
                qrCode: true
            },
            {
                idNganHang: 3,
                maNganHang: "VTB",
                tenNganHang: "Ngân hàng TMCP Công thương Việt Nam (VietinBank)",
                congThanhToan: "VietinBank Gateway",
                active: true,
                qrCode: false
            },
            {
                idNganHang: 4,
                maNganHang: "ACB",
                tenNganHang: "Ngân hàng TMCP Á Châu (ACB)",
                congThanhToan: "ACB Gateway",
                active: false,
                qrCode: true
            },
            {
                idNganHang: 5,
                maNganHang: "TCB",
                tenNganHang: "Ngân hàng TMCP Kỹ thương Việt Nam (Techcombank)",
                congThanhToan: "Techcombank Gateway",
                active: true,
                qrCode: true
            }
        ];

        // Hàm gọi API (mock)
        async function callAPI(endpoint) {
            // Trong thực tế, sử dụng fetch() để gọi API
            // return fetch(API_BASE_URL + endpoint).then(res => res.json());
            
            // Mock response
            await new Promise(resolve => setTimeout(resolve, 500)); // Giả lập delay
            
            if (endpoint.includes('active-qr')) {
                return { data: mockBankData.filter(b => b.active && b.qrCode) };
            } else if (endpoint.includes('qr-supported')) {
                return { data: mockBankData.filter(b => b.qrCode) };
            } else if (endpoint.includes('active')) {
                return { data: mockBankData.filter(b => b.active) };
            } else {
                return { data: mockBankData };
            }
        }

        // Hàm load dữ liệu cho combobox
        async function loadComboboxData(selectId, endpoint) {
            try {
                const response = await callAPI(endpoint);
                const select = document.getElementById(selectId);
                
                // Xóa options cũ (trừ option đầu tiên)
                while (select.children.length > 1) {
                    select.removeChild(select.lastChild);
                }
                
                // Thêm options mới
                response.data.forEach(bank => {
                    const option = document.createElement('option');
                    option.value = bank.idNganHang;
                    option.textContent = `${bank.tenNganHang} ${bank.qrCode ? '(QR)' : ''}`;
                    option.dataset.bank = JSON.stringify(bank);
                    select.appendChild(option);
                });
                
                console.log(`Loaded ${response.data.length} banks for ${selectId}`);
            } catch (error) {
                console.error('Error loading combobox data:', error);
            }
        }

        // Hàm hiển thị thông tin ngân hàng
        function displayBankInfo(bank, containerId) {
            const container = document.getElementById(containerId);
            if (!bank) {
                container.innerHTML = '<em>Chưa chọn ngân hàng nào</em>';
                return;
            }
            
            container.innerHTML = `
                <div class="bank-item">
                    <strong>Tên:</strong> ${bank.tenNganHang}<br>
                    <strong>Mã:</strong> ${bank.maNganHang}<br>
                    <strong>Cổng thanh toán:</strong> ${bank.congThanhToan}<br>
                    <strong>Trạng thái:</strong> 
                    <span class="badge ${bank.active ? 'badge-active' : 'badge-inactive'}">
                        ${bank.active ? 'Hoạt động' : 'Không hoạt động'}
                    </span><br>
                    <strong>Hỗ trợ QR:</strong> 
                    <span class="badge ${bank.qrCode ? 'badge-qr' : 'badge-inactive'}">
                        ${bank.qrCode ? 'Có' : 'Không'}
                    </span>
                </div>
            `;
        }

        // Hàm hiển thị danh sách ngân hàng
        function displayBankList(banks) {
            const container = document.getElementById('bankList');
            if (!banks || banks.length === 0) {
                container.innerHTML = '<em>Không có dữ liệu</em>';
                return;
            }
            
            let html = `<div><strong>Tổng số: ${banks.length} ngân hàng</strong></div>`;
            banks.forEach(bank => {
                html += `
                    <div class="bank-item">
                        <strong>${bank.tenNganHang}</strong> (${bank.maNganHang})<br>
                        <small>Cổng: ${bank.congThanhToan}</small><br>
                        <span class="badge ${bank.active ? 'badge-active' : 'badge-inactive'}">
                            ${bank.active ? 'Hoạt động' : 'Không hoạt động'}
                        </span>
                        <span class="badge ${bank.qrCode ? 'badge-qr' : 'badge-inactive'}">
                            ${bank.qrCode ? 'QR Code' : 'Không QR'}
                        </span>
                    </div>
                `;
            });
            container.innerHTML = html;
        }

        // Khởi tạo khi trang load
        document.addEventListener('DOMContentLoaded', function() {
            // Load dữ liệu cho các combobox
            loadComboboxData('bankSelect1', '/for-combobox');
            loadComboboxData('bankSelect2', '/for-combobox/active');
            loadComboboxData('bankSelect3', '/for-combobox/qr-supported');
            loadComboboxData('bankSelect4', '/for-combobox/active');
            
            // Khởi tạo Select2 cho combobox tìm kiếm
            setTimeout(() => {
                $('#bankSelect4').select2({
                    placeholder: "Tìm kiếm ngân hàng...",
                    allowClear: true
                });
            }, 1000);
            
            // Event handlers cho các combobox
            ['bankSelect1', 'bankSelect2', 'bankSelect3'].forEach((selectId, index) => {
                document.getElementById(selectId).addEventListener('change', function() {
                    const selectedOption = this.options[this.selectedIndex];
                    const bank = selectedOption.dataset.bank ? JSON.parse(selectedOption.dataset.bank) : null;
                    displayBankInfo(bank, `bankInfo${index + 1}`);
                });
            });
            
            // Event handler cho Select2
            $('#bankSelect4').on('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                const bank = selectedOption.dataset.bank ? JSON.parse(selectedOption.dataset.bank) : null;
                displayBankInfo(bank, 'bankInfo4');
            });
            
            // Event handlers cho các nút load danh sách
            document.getElementById('loadAllBanks').addEventListener('click', async function() {
                const response = await callAPI('/for-combobox');
                displayBankList(response.data);
            });
            
            document.getElementById('loadActiveBanks').addEventListener('click', async function() {
                const response = await callAPI('/for-combobox/active');
                displayBankList(response.data);
            });
            
            document.getElementById('loadQrBanks').addEventListener('click', async function() {
                const response = await callAPI('/for-combobox/qr-supported');
                displayBankList(response.data);
            });
        });
    </script>
</body>
</html>
